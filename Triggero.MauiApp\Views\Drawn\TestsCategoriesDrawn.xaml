﻿<?xml version="1.0" encoding="UTF-8" ?>
<draw:Canvas
    x:Class="Triggero.MauiMobileApp.Views.TestsCategoriesDrawn"
    xmlns="http://schemas.microsoft.com/dotnet/2021/maui"
    xmlns:x="http://schemas.microsoft.com/winfx/2009/xaml"
    xmlns:viewModels="clr-namespace:Triggero.MauiMobileApp.ViewModels"
    xmlns:draw="http://schemas.appomobi.com/drawnUi/2023/draw"
    xmlns:triggeroV2="clr-namespace:Triggero.MauiMobileApp"
    x:Name="this"
    x:DataType="viewModels:BaseCategoriesViewModel"
    Gestures="Lock"
    RenderingMode="Accelerated"
    HorizontalOptions="Fill"
    VerticalOptions="Fill">

    <draw:SkiaLayout
        HorizontalOptions="Fill"
        VerticalOptions="Fill">

        <draw:SkiaShape
            Padding="0"
            BackgroundColor="White"
            CornerRadius="15,15,0,0"
            HorizontalOptions="Fill"
            UseCache="Image"
            VerticalOptions="Fill" />

        <draw:SkiaScroll
            Margin="20,0,20,0"
            FrictionScrolled="0.35"
            HorizontalOptions="Fill"
            VerticalOptions="Fill">

            <draw:SkiaLayout
                x:Name="StackCells"
                Padding="0,12,0,0"
                CommandChildTapped="{Binding CommandChildTapped}"
                HorizontalOptions="Fill"
                ItemTemplate="{Binding ItemTemplate}"
                ItemsSource="{Binding Items}"
                RecyclingTemplate="Disabled"
                Spacing="12"
                Type="Column"
                UseCache="Operations" />

        </draw:SkiaScroll>

        <!--<draw:SkiaLabel
            UseCache="Operations"
            Margin="8"
            BackgroundColor="Black"
            HorizontalOptions="Start"
            InputTransparent="True"
            Text="{Binding Source={x:Reference StackCells}, Path=DebugString}"
            VerticalOptions="Start" />-->

        <!--  FPS  -->
        <draw:SkiaLabelFps
            IsVisible="{x:Static triggeroV2:Globals.ShowFPS}"
            Margin="0,0,4,84"
            BackgroundColor="DarkRed"
            ForceRefresh="False"
            HorizontalOptions="End"
            Rotation="-45"
            TextColor="White"
            VerticalOptions="End" />




    </draw:SkiaLayout>
</draw:Canvas>
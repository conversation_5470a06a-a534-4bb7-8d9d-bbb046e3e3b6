﻿<?xml version="1.0" encoding="UTF-8"?>
<ContentView xmlns="http://schemas.microsoft.com/dotnet/2021/maui" 
             xmlns:x="http://schemas.microsoft.com/winfx/2009/xaml"
             
             xmlns:controls="clr-namespace:Triggero.MauiMobileApp.Controls"
             x:Class="Triggero.Controls.Cards.Tracker.Factors.FactorDetailsGroupCard">
  <ContentView.Content>

        <Frame
              CornerRadius="10"
              Margin="0,0,5,0"
              BackgroundColor="#FFFFFF"
              HasShadow="False"
              Padding="20">
            <Frame.Shadow>
                <Shadow Brush="#27527A"
                        Offset="2,2"
                        Radius="12"
                        Opacity="0.06" />
            </Frame.Shadow>
                <StackLayout
                    Spacing="12">
                    <Label 
                        HeightRequest="25"
                        x:Name="titleLabel"
                        TextColor="Black"
                        FontSize="{OnPlatform Android=17,iOS=17}"
                        FontAttributes="Bold"
                        HorizontalOptions="Start"/>

                    <Grid
                        x:Name="gridLayout"
                        RowSpacing="14"
                        ColumnSpacing="14">
                        <Grid.ColumnDefinitions>
                            <ColumnDefinition Width="1*"/>
                            <ColumnDefinition Width="1*"/>
                            <ColumnDefinition Width="1*"/>
                        </Grid.ColumnDefinitions>
                        <Grid.RowDefinitions>
                            <RowDefinition Height="86"/>
                        </Grid.RowDefinitions>
                    </Grid>

                </StackLayout>
        </Frame>
    </ContentView.Content>
</ContentView>
﻿<?xml version="1.0" encoding="utf-8" ?>
<ContentPage
    x:Class="Triggero.MauiMobileApp.Views.Pages.MoodTracker.TrackerMainPage"
    xmlns="http://schemas.microsoft.com/dotnet/2021/maui"
    xmlns:x="http://schemas.microsoft.com/winfx/2009/xaml"
    xmlns:app="clr-namespace:Triggero"
    xmlns:mobile="clr-namespace:Triggero.MauiMobileApp"
    xmlns:moodtracker="clr-namespace:Triggero.MauiMobileApp.Views.MoodTracker"
    xmlns:pancakeview="clr-namespace:Triggero.MauiMobileApp.Controls"
    xmlns:triggeroV2="clr-namespace:Triggero.MauiMobileApp"
    x:Name="this"
    Background="White"
    BackgroundColor="White">
    <ContentPage.Content>

        <Grid
            Padding="0"
            BackgroundColor="#FFFFFF"
            RowSpacing="0">

            <Grid.RowDefinitions>
                <RowDefinition Height="{x:Static triggeroV2:Globals.TopInsets}" />
                <RowDefinition Height="270" />
                <RowDefinition Height="*" />
            </Grid.RowDefinitions>

            <Image
                Grid.Row="0"
                Grid.RowSpan="3"
                Aspect="Fill"
                Source="trackerBgGradient.png" />

            <Grid Grid.Row="1">

                <Grid.RowDefinitions>
                    <RowDefinition Height="120" />
                    <RowDefinition Height="*" />
                </Grid.RowDefinitions>

                <Grid Grid.Row="0">

                    <Label
                        Margin="20,0,0,0"
                        FontAttributes="Bold"
                        FontSize="27"
                        HorizontalOptions="Start"
                        Text="{Binding Source={x:Static mobile:App.This}, Path=Interface.MoodTracker.TrackerMainPage.MoodTracker}"
                        TextColor="{x:StaticResource greyTextColor}"
                        VerticalOptions="End" />

                    <ImageButton
                        Margin="0,0,25,0"
                        BackgroundColor="Transparent"
                        Command="{Binding Source={x:Reference this}, Path=Close}"
                        CornerRadius="0"
                        HeightRequest="14"
                        HorizontalOptions="End"
                        Source="close.png"
                        VerticalOptions="Center"
                        WidthRequest="14" />

                </Grid>

                <Grid Grid.Row="1">
                    <Grid
                        Margin="20,0,20,0"
                        VerticalOptions="Center">
                        <Grid.ColumnDefinitions>
                            <ColumnDefinition Width="1*" />
                            <ColumnDefinition Width="1*" />
                            <ColumnDefinition Width="1*" />
                            <ColumnDefinition Width="1*" />
                        </Grid.ColumnDefinitions>

                        <!--  BTN Календарь  -->
                        <Frame
                            Grid.Column="0"
                            Padding="0"
                            BackgroundColor="Transparent"
                            CornerRadius="16"
                            HasShadow="False"
                            HeightRequest="90"
                            HorizontalOptions="Center"
                            IsClippedToBounds="True"
                            VerticalOptions="Center"
                            WidthRequest="75">

                            <Frame.GestureRecognizers>
                                <TapGestureRecognizer Command="{Binding Source={x:Reference this}, Path=ShowCalendar}" />
                            </Frame.GestureRecognizers>

                            <Grid>
                                <Grid.RowDefinitions>
                                    <RowDefinition Height="60" />
                                    <RowDefinition Height="30" />
                                </Grid.RowDefinitions>

                                <Grid
                                    Grid.RowSpan="2"
                                    Opacity="0.3">
                                    <Grid.Background>
                                        <LinearGradientBrush>
                                            <LinearGradientBrush.GradientStops>
                                                <GradientStop Offset="0.1" Color="#F9EDC7" />
                                                <GradientStop Offset="1.0" Color="#ECA069" />
                                            </LinearGradientBrush.GradientStops>
                                        </LinearGradientBrush>
                                    </Grid.Background>
                                </Grid>

                                <Grid Grid.Row="0">
                                    <Image
                                        HeightRequest="47"
                                        HorizontalOptions="Center"
                                        Source="trackerCalendar.png"
                                        VerticalOptions="Center"
                                        WidthRequest="43" />
                                </Grid>

                                <Grid Grid.Row="1">
                                    <Label
                                        FontSize="{OnPlatform Android=10,
                                                              iOS=12}"
                                        HorizontalOptions="Center"
                                        Text="{Binding Source={x:Static mobile:App.This}, Path=Interface.MoodTracker.TrackerMainPage.Calendar}"
                                        TextColor="{x:StaticResource greyTextColor}"
                                        VerticalOptions="Start" />
                                </Grid>

                            </Grid>
                        </Frame>

                        <!--  BTN Статистика  -->
                        <Frame
                            Grid.Column="1"
                            Padding="0"
                            BackgroundColor="Transparent"
                            CornerRadius="16"
                            HasShadow="False"
                            HeightRequest="90"
                            HorizontalOptions="Center"
                            IsClippedToBounds="True"
                            VerticalOptions="Center"
                            WidthRequest="75">
                            <Frame.GestureRecognizers>
                                <TapGestureRecognizer Command="{Binding Source={x:Reference this}, Path=ShowStats}" />
                            </Frame.GestureRecognizers>
                            <Grid>
                                <Grid.RowDefinitions>
                                    <RowDefinition Height="60" />
                                    <RowDefinition Height="30" />
                                </Grid.RowDefinitions>

                                <Grid
                                    Grid.RowSpan="2"
                                    Opacity="0.3">
                                    <Grid.Background>
                                        <LinearGradientBrush>
                                            <LinearGradientBrush.GradientStops>
                                                <GradientStop Offset="0.1" Color="#BFFFFB" />
                                                <GradientStop Offset="1.0" Color="#2448DC" />
                                            </LinearGradientBrush.GradientStops>
                                        </LinearGradientBrush>
                                    </Grid.Background>
                                </Grid>

                                <Grid Grid.Row="0">
                                    <Image
                                        HeightRequest="51"
                                        HorizontalOptions="Center"
                                        Source="trackerStats.png"
                                        VerticalOptions="Center"
                                        WidthRequest="47" />
                                </Grid>

                                <Grid Grid.Row="1">
                                    <Label
                                        FontSize="{OnPlatform Android=10,
                                                              iOS=12}"
                                        HorizontalOptions="Center"
                                        Text="{Binding Source={x:Static mobile:App.This}, Path=Interface.MoodTracker.TrackerMainPage.Stats}"
                                        TextColor="{x:StaticResource greyTextColor}"
                                        VerticalOptions="Start" />
                                </Grid>

                            </Grid>
                        </Frame>

                        <!--  BTN Заметки  -->
                        <Frame
                            Grid.Column="2"
                            Padding="0"
                            BackgroundColor="Transparent"
                            CornerRadius="16"
                            HasShadow="False"
                            HeightRequest="90"
                            HorizontalOptions="Center"
                            IsClippedToBounds="True"
                            VerticalOptions="Center"
                            WidthRequest="75">
                            <Frame.GestureRecognizers>
                                <TapGestureRecognizer Command="{Binding Source={x:Reference this}, Path=ShowNotes}" />
                            </Frame.GestureRecognizers>

                            <Grid>
                                <Grid.RowDefinitions>
                                    <RowDefinition Height="60" />
                                    <RowDefinition Height="30" />
                                </Grid.RowDefinitions>

                                <Grid Grid.RowSpan="2">
                                    <Grid.Background>
                                        <LinearGradientBrush>
                                            <LinearGradientBrush.GradientStops>
                                                <GradientStop Offset="0.1" Color="#F7FFF3" />
                                                <GradientStop Offset="0.5" Color="#EBFFE2" />
                                                <GradientStop Offset="1.0" Color="#D1EFFF" />
                                            </LinearGradientBrush.GradientStops>
                                        </LinearGradientBrush>
                                    </Grid.Background>
                                </Grid>

                                <Grid Grid.Row="0">
                                    <Image
                                        HeightRequest="46"
                                        HorizontalOptions="Center"
                                        Source="trackerNotes.png"
                                        VerticalOptions="Center"
                                        WidthRequest="53" />
                                </Grid>

                                <Grid Grid.Row="1">
                                    <Label
                                        FontSize="{OnPlatform Android=10,
                                                              iOS=12}"
                                        HorizontalOptions="Center"
                                        Text="{Binding Source={x:Static mobile:App.This}, Path=Interface.MoodTracker.TrackerMainPage.Notes}"
                                        TextColor="{x:StaticResource greyTextColor}"
                                        VerticalOptions="Start" />
                                </Grid>

                            </Grid>
                        </Frame>

                    </Grid>
                </Grid>
            </Grid>

            <pancakeview:PancakeView
                Grid.Row="2"
                Padding="10,10,10,0"
                BackgroundColor="#FFFFFF"
                StrokeShape="RoundRectangle 15,15,0,0">
                <Grid>

                    <!--  Календарь  -->
                    <Grid IsVisible="False">
                        <Grid.Style>
                            <Style TargetType="Grid">
                                <Style.Triggers>
                                    <DataTrigger
                                        Binding="{Binding Source={x:Reference this}, Path=MoodTrackerSectionType}"
                                        TargetType="Grid"
                                        Value="Calendar">
                                        <Setter Property="IsVisible" Value="True" />
                                    </DataTrigger>
                                </Style.Triggers>
                            </Style>
                        </Grid.Style>

                        <!--  TrackerCalendarView  -->
                        <moodtracker:TrackerCalendarView IsVisible="False">
                            <moodtracker:TrackerCalendarView.Style>
                                <Style TargetType="moodtracker:TrackerCalendarView">
                                    <Style.Triggers>
                                        <DataTrigger
                                            Binding="{Binding Source={x:Reference this}, Path=MoodTrackerSectionType}"
                                            TargetType="moodtracker:TrackerCalendarView"
                                            Value="Calendar">
                                            <Setter Property="IsVisible" Value="True" />
                                        </DataTrigger>
                                    </Style.Triggers>
                                </Style>
                            </moodtracker:TrackerCalendarView.Style>
                        </moodtracker:TrackerCalendarView>

                    </Grid>

                    <!--  Статистика  -->
                    <Grid IsVisible="False">
                        <Grid.Style>
                            <Style TargetType="Grid">
                                <Style.Triggers>
                                    <DataTrigger
                                        Binding="{Binding Source={x:Reference this}, Path=MoodTrackerSectionType}"
                                        TargetType="Grid"
                                        Value="Stats">
                                        <Setter Property="IsVisible" Value="True" />
                                    </DataTrigger>
                                </Style.Triggers>
                            </Style>
                        </Grid.Style>

                        <moodtracker:TrackerStatsView IsVisible="False">
                            <moodtracker:TrackerStatsView.Style>
                                <Style TargetType="moodtracker:TrackerStatsView">
                                    <Style.Triggers>
                                        <DataTrigger
                                            Binding="{Binding Source={x:Reference this}, Path=MoodTrackerSectionType}"
                                            TargetType="moodtracker:TrackerStatsView"
                                            Value="Stats">
                                            <Setter Property="IsVisible" Value="True" />
                                        </DataTrigger>
                                    </Style.Triggers>
                                </Style>
                            </moodtracker:TrackerStatsView.Style>
                        </moodtracker:TrackerStatsView>

                    </Grid>

                    <!--  Заметки  -->
                    <Grid IsVisible="False">
                        <Grid.Style>
                            <Style TargetType="Grid">
                                <Style.Triggers>
                                    <DataTrigger
                                        Binding="{Binding Source={x:Reference this}, Path=MoodTrackerSectionType}"
                                        TargetType="Grid"
                                        Value="Notes">
                                        <Setter Property="IsVisible" Value="True" />
                                    </DataTrigger>
                                </Style.Triggers>
                            </Style>
                        </Grid.Style>

                        <moodtracker:TrackerNotesView>
                            <!--<moodtracker:TrackerNotesView.Style>
                                <Style TargetType="moodtracker:TrackerNotesView">
                                    <Style.Triggers>
                                        <DataTrigger TargetType="moodtracker:TrackerNotesView"
                                                 Binding="{Binding Source={x:Reference this},Path=MoodTrackerSectionType}"
                                                 Value="Notes">
                                            <Setter Property="IsVisible" Value="True"/>
                                        </DataTrigger>
                                    </Style.Triggers>
                                </Style>
                            </moodtracker:TrackerNotesView.Style>-->
                        </moodtracker:TrackerNotesView>

                    </Grid>


                </Grid>

            </pancakeview:PancakeView>

        </Grid>
    </ContentPage.Content>
</ContentPage>
﻿<?xml version="1.0" encoding="UTF-8" ?>
<ContentView
    x:Class="Triggero.MauiMobileApp.Views.ListTestsView"
    xmlns="http://schemas.microsoft.com/dotnet/2021/maui"
    xmlns:x="http://schemas.microsoft.com/winfx/2009/xaml"
    xmlns:pancakeview="clr-namespace:Triggero.MauiMobileApp.Controls"
    xmlns:viewModels="clr-namespace:Triggero.MauiMobileApp.ViewModels"
    xmlns:draw="http://schemas.appomobi.com/drawnUi/2023/draw"
    xmlns:triggeroV2="clr-namespace:Triggero.MauiMobileApp"
    x:Name="this"
    x:DataType="viewModels:ElementsListViewModel">

    <ContentView.Content>
        <Grid
            Padding="0"
            RowSpacing="0">
            <Grid.Background>
                <LinearGradientBrush StartPoint="0,0" EndPoint="0,1">
                    <LinearGradientBrush.GradientStops>
                        <GradientStop Offset="0.0" Color="#FFFFFF" />
                        <GradientStop Offset="1.0" Color="#FDCE72" />
                    </LinearGradientBrush.GradientStops>
                </LinearGradientBrush>
            </Grid.Background>

            <Grid.RowDefinitions>
                <RowDefinition Height="156" />
                <RowDefinition Height="*" />
            </Grid.RowDefinitions>

            <Grid Grid.Row="0">


                <Grid
                    x:Name="pageBackNavigationGrid"
                    Margin="20,0,20,30"
                    VerticalOptions="End">

                    <Grid.ColumnDefinitions>
                        <ColumnDefinition Width="44" />
                        <ColumnDefinition Width="*" />
                    </Grid.ColumnDefinitions>

                    <ImageButton
                        Grid.Column="0"
                        BackgroundColor="Transparent"
                        Command="{Binding Source={x:Reference this}, Path=GoBack}"
                        CornerRadius="0"
                        HeightRequest="44"
                        HorizontalOptions="Center"
                        Source="buttonBackBordered.png"
                        VerticalOptions="Center"
                        WidthRequest="44" />

                    <Label
                        x:Name="categoryPageLabel"
                        Grid.Column="1"
                        Margin="20,0,0,0"
                        Text="{Binding Title}"
                        Style="{x:StaticResource StyleHeaderText}"
                        VerticalOptions="Center" />

                </Grid>

            </Grid>

            <pancakeview:PancakeView
                Grid.Row="1"
                Padding="0"
                BackgroundColor="#FFFFFF"
                StrokeShape="RoundRectangle 15,15,0,0">
                <Grid>
                    <Grid.RowDefinitions>
                        <RowDefinition Height="20" />
                        <RowDefinition Height="45" />
                        <RowDefinition Height="*" />
                    </Grid.RowDefinitions>

                   
                    <Grid Grid.Row="1">

                        <Frame
                            Margin="20,0,20,0"
                            Padding="0"
                            BackgroundColor="Transparent"
                            BorderColor="{x:StaticResource lightBlueColor}"
                            CornerRadius="12"
                            HasShadow="False"
                            HeightRequest="32"
                            VerticalOptions="Center">
                            <Grid>
                                <Grid.ColumnDefinitions>
                                    <ColumnDefinition Width="1*" />
                                    <ColumnDefinition Width="1*" />
                                </Grid.ColumnDefinitions>

                                <RadioButton
                                    Grid.Column="0"
                                    CheckedChanged="filterByNewCheckedChanged"
                                    Content="{Binding Source={x:Static triggeroV2:App.This}, Path=Interface.Library.Library.FilterNew}"
                                    IsChecked="True"
                                    Style="{x:StaticResource yellow_rb}" />

                                <RadioButton
                                    Grid.Column="1"
                                    CheckedChanged="filterByWatchesCheckedChanged"
                                    Content="{Binding Source={x:Static triggeroV2:App.This}, Path=Interface.Library.Library.FilterPopular}"
                                    Style="{x:StaticResource yellow_rb}" />

                            </Grid>
                        </Frame>

                    </Grid>

                    <!--<Grid Grid.Row="2" x:Name="scrollGrid">

                        <CollectionView
                            Margin="20,0,20,0"
                            x:Name="collection">
                            <CollectionView.ItemTemplate>
                                <DataTemplate>
                                    <templates:TestCard
                                        HeightRequest="104"
                                        VerticalOptions="Start"
                                        HorizontalOptions="Fill"
                                        Padding="0,6,0,0" />

                                </DataTemplate>
                            </CollectionView.ItemTemplate>
                        </CollectionView>

                    </Grid>-->

                    <!--  DRAWN  -->
                    <draw:Canvas
                        x:Name="DrawnCanvas"
                        Grid.Row="2"
                        Margin="0,6,0,0"
                        Gestures="Lock"
                        RenderingMode="Accelerated"
                        HorizontalOptions="Fill"
                        VerticalOptions="Fill">

                        <draw:SkiaLayout
                            HorizontalOptions="Fill"
                            VerticalOptions="Fill">

                            <draw:SkiaScroll
                                FrictionScrolled="0.35"
                                HorizontalOptions="Fill"
                                VerticalOptions="Fill">

                                <draw:SkiaLayout
                                    UseCache="None"
                                    VirtualisationInflated="350"
                                    x:Name="StackCells"
                                    Padding="0,0,0,0"
                                    HorizontalOptions="Fill"
                                    ItemTemplate="{Binding ItemTemplate}"
                                    ItemsSource="{Binding Items}"
                                    RecyclingTemplate="Enabled"
                                    Spacing="-8"
                                    Type="Column" />

                            </draw:SkiaScroll>

                            <!--  FPS  -->
                            <draw:SkiaLabelFps
                                IsVisible="{x:Static triggeroV2:Globals.ShowFPS}"
                                Margin="0,0,4,84"
                                BackgroundColor="DarkRed"
                                ForceRefresh="False"
                                HorizontalOptions="End"
                                Rotation="-45"
                                TextColor="White"
                                VerticalOptions="End" />

                        </draw:SkiaLayout>

                    </draw:Canvas>


                </Grid>
            </pancakeview:PancakeView>




        </Grid>
    </ContentView.Content>
</ContentView>
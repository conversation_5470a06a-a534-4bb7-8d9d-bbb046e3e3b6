﻿<?xml version = "1.0" encoding = "UTF-8" ?>
<Application xmlns="http://schemas.microsoft.com/dotnet/2021/maui"
             xmlns:x="http://schemas.microsoft.com/winfx/2009/xaml"
             xmlns:local="clr-namespace:Triggero.MauiMobileApp"
             xmlns:converters="clr-namespace:Triggero.MauiMobileApp.Converters"
             xmlns:draw="http://schemas.appomobi.com/drawnUi/2023/draw"
             xmlns:toolkit="http://schemas.microsoft.com/dotnet/2022/maui/toolkit"
             xmlns:converters1="clr-namespace:MarkupCreator.Converters"
             x:Class="Triggero.MauiMobileApp.App">
    <Application.Resources>
        <ResourceDictionary>

            <ResourceDictionary.MergedDictionaries>
                <ResourceDictionary Source="Resources/Styles/MainResources.xaml" />
                <ResourceDictionary Source="Resources/Styles/SfSwitchStyles.xaml" />
                <ResourceDictionary Source="Resources/Styles/TextEditStyles.xaml" />
                <ResourceDictionary Source="Resources/Styles/RadioButtonStyles.xaml" />
                <ResourceDictionary Source="Resources/Styles/CheckBoxStyles.xaml" />
                <ResourceDictionary Source="Resources/Styles/ButtonStyles.xaml" />
            </ResourceDictionary.MergedDictionaries>

            <!-- Converters -->
            <converters:BoolToColorConverter x:Key="BoolToColorConverter" />
            <converters1:TimeSpanToStringConverter x:Key="TimeSpanToStringConverter" />
            <converters1:IntegerNotZeroConverter x:Key="IntegerNotZeroConverter" />
            <converters1:IntegerIsZeroConverter x:Key="IntegerIsZeroConverter" />
            <toolkit:InvertedBoolConverter x:Key="NotConverter" />
            <toolkit:IsStringNullOrEmptyConverter x:Key="IsNotNullOrEmptyConverter" />
            <converters1:CompareIntegersConverter x:Key="CompareIntegersConverter" />

            <Color x:Key="ColorAccent">#FDCE72</Color>
            <Color x:Key="ColorPrimary">#10ABB8</Color>
            <Color x:Key="ColorPrimaryLight">#CEE3F4</Color>
            <Color x:Key="ColorText">#363B40</Color>
            <Color x:Key="ColorTextGray">#B2363B40</Color>

            <Style
                ApplyToDerivedTypes="True"
                TargetType="Label">
                <Setter Property="FontFamily" Value="FontText" />
                <Setter Property="FontSize" Value="12" />
                <Setter Property="TextColor" Value="{x:StaticResource ColorText}" />
            </Style>

            <Style
                x:Key="StyleBtnText"
                ApplyToDerivedTypes="True"
                TargetType="Label">
                <Setter Property="FontFamily" Value="FontTextBold" />
                <Setter Property="FontSize" Value="14" />
            </Style>

            <Style
                x:Key="StyleHeaderNavigation"
                ApplyToDerivedTypes="True"
                TargetType="Label">
                <Setter Property="FontFamily" Value="FontTextBold" />
                <Setter Property="FontSize" Value="16" />
            </Style>

            <Style
                x:Key="StyleHeaderText"
                ApplyToDerivedTypes="True"
                TargetType="Label">
                <Setter Property="FontFamily" Value="FontTextBold" />
                <Setter Property="FontSize" Value="24" />
            </Style>

            <Style
                x:Key="StyleArticleTitle"
                ApplyToDerivedTypes="True"
                TargetType="Label">
                <Setter Property="FontFamily" Value="FontTextSemiBold" />
                <Setter Property="FontSize" Value="20" />
                <Setter Property="TextColor" Value="Black" />
            </Style>

            <Style
                x:Key="StyleArticleSubTitle"
                ApplyToDerivedTypes="True"
                TargetType="Label">
                <Setter Property="FontSize" Value="12" />
                <Setter Property="TextColor" Value="#7F000000" />
            </Style>

            <Style
                x:Key="StyleArticleText"
                ApplyToDerivedTypes="True"
                TargetType="Label">
                <Setter Property="FontSize" Value="15" />
            </Style>

            <Style
                x:Key="StyleHeaderTextDrawn"
                ApplyToDerivedTypes="True"
                TargetType="draw:SkiaLabel">
                <Setter Property="ParagraphSpacing" Value="0.1" />
                <Setter Property="FontFamily" Value="FontTextBold" />
                <Setter Property="FontSize" Value="24" />
            </Style>

            <Style
                ApplyToDerivedTypes="True"
                TargetType="draw:SkiaLabel">
                <Setter Property="FontFamily" Value="FontText" />
                <Setter Property="FontSize" Value="12" />
                <Setter Property="LineSpacing" Value="1.0" />
                <Setter Property="TextColor" Value="{x:StaticResource ColorText}" />
            </Style>

            <Style
                x:Key="StyleBtnTextDrawn"
                ApplyToDerivedTypes="True"
                TargetType="draw:SkiaLabel">
                <Setter Property="FontFamily" Value="FontTextBold" />
                <Setter Property="FontSize" Value="14" />
                <Setter Property="LineSpacing" Value="0.9" />
                <Setter Property="ParagraphSpacing" Value="-0.15" />
            </Style>

            <Style
                ApplyToDerivedTypes="True"
                TargetType="ContentPage">
                <!--<Setter Property="Background" Value="White" />-->
                <Setter Property="BackgroundColor" Value="White" />
            </Style>

            <Style
                ApplyToDerivedTypes="True"
                TargetType="draw:SkiaShape">
                <Setter Property="CornerRadius" Value="15" />
            </Style>

            <Style
                ApplyToDerivedTypes="True"
                TargetType="NavigationPage">
                <!--<Setter Property="Background" Value="White" />-->
                <Setter Property="BackgroundColor" Value="White" />
            </Style>

            <Style
                ApplyToDerivedTypes="True"
                TargetType="ActivityIndicator">
                <Setter Property="Color" Value="{x:StaticResource ColorPrimary}" />
            </Style>

        </ResourceDictionary>
    </Application.Resources>
</Application>

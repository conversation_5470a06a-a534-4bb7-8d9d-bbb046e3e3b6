﻿<?xml version="1.0" encoding="utf-8" ?>
<ContentPage xmlns="http://schemas.microsoft.com/dotnet/2021/maui"
             xmlns:x="http://schemas.microsoft.com/winfx/2009/xaml"
             
             x:Class="Triggero.MauiMobileApp.Views.Pages.Auth.TutorialCompletedPage"
             xmlns:app="clr-namespace:Triggero"
             
             xmlns:mobile="clr-namespace:Triggero.MauiMobileApp"
             xmlns:controls="clr-namespace:Triggero.MauiMobileApp.Controls"
             x:Name="this">
    <ContentPage.Content>
        <Grid>

            <Image
                Aspect="Fill"
                VerticalOptions="Fill"
                HorizontalOptions="Fill"
                Source="lightBlueGradientBg.png"/>

            <Frame
                Margin="30,0,30,0"
                HeightRequest="400"
                VerticalOptions="Center"
                CornerRadius="16"
                BackgroundColor="White"
                HasShadow="False"
                HorizontalOptions="Fill"
                Padding="0">
                <Frame.Shadow>
                    <Shadow Brush="#27527A"
                            Offset="2,2"
                            Radius="12"
                            Opacity="0.06" />
                </Frame.Shadow>
                    <Grid>
                        <StackLayout
                            VerticalOptions="Start"
                            HorizontalOptions="Fill"
                            Spacing="0">


                            <!--<Image 
                                Margin="0,30,0,0"
                                HeightRequest="120"
                                WidthRequest="100"
                                HorizontalOptions="Center"
                                VerticalOptions="Start"
                                Source="tutorialFinishedImg.png"/>-->

                            <Grid
                                x:Name="animatedLayout"
                                Margin="0,0,0,0"
                                HeightRequest="240"
                                WidthRequest="200"
                                HorizontalOptions="Center"
                                VerticalOptions="Start">

                                <Image
                                    x:Name="notAnimatedImg"
                                    Margin="0,0,0,0"
                                    HeightRequest="240"
                                    WidthRequest="200"
                                    HorizontalOptions="Center"
                                    VerticalOptions="Start"
                                    Source="animationDogWithComp.png"/>

                            </Grid>
                            
                            


                            <StackLayout
                                Spacing="1"
                                HorizontalOptions="Center"
                                Margin="0,0,0,0">

                                <Label 
                                    TextColor="{x:StaticResource greyTextColor}"
                                    FontSize="{OnPlatform Android=14,iOS=17}"
                                    VerticalOptions="Start"
                                    HorizontalOptions="Center"
                                    HorizontalTextAlignment="Center"
                                    WidthRequest="{OnPlatform Android=150,iOS=250}"
                                    Text="{Binding Source={x:Static mobile:App.This},Path=Interface.Tutorial.TutorialCompletedPage.Text}"/>

                            </StackLayout>


                            <!--<Button 
                                Command="{Binding Source={x:Reference this},Path=GoToTrial}"
                                Margin="33,40,33,0"
                                VerticalOptions="Start"
                                HeightRequest="56"
                                Style="{x:StaticResource yellow_btn}"
                                Text="{Binding Source={x:Static app:App.This},Path=Interface.Tutorial.TutorialCompletedPage.GoMain}"/>-->

                            <ImageButton 
                                Command="{Binding Source={x:Reference this},Path=GoToTrial}"
                                Margin="0,30,0,0"
                                WidthRequest="92"
                                HeightRequest="64"
                                HorizontalOptions="Center"
                                VerticalOptions="Start"
                                CornerRadius="0"
                                BackgroundColor="Transparent"
                                Source="yellowContinueBtn.png"/>


                        </StackLayout>
                    </Grid>
                </Frame>

        </Grid>
    </ContentPage.Content>
</ContentPage>
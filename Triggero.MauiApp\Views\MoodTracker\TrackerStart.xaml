﻿<?xml version="1.0" encoding="utf-8" ?>
<ContentPage
    x:Class="Triggero.MauiMobileApp.Views.Pages.MoodTracker.TrackerStart"
    xmlns="http://schemas.microsoft.com/dotnet/2021/maui"
    xmlns:x="http://schemas.microsoft.com/winfx/2009/xaml"
    xmlns:app="clr-namespace:Triggero"
    xmlns:mobile="clr-namespace:Triggero.MauiMobileApp"
    xmlns:parts="clr-namespace:Triggero.Controls.Parts"
    x:Name="this"
    Background="White"
    BackgroundColor="White">
    <ContentPage.Content>
        <Grid>
            <Image
                Aspect="Fill"
                Source="mootTrackerStartBlur.png">
                <Image.GestureRecognizers>
                    <TapGestureRecognizer Command="{Binding Source={x:Reference this}, Path=Close}" />
                </Image.GestureRecognizers>
            </Image>

            <StackLayout
                Margin="20,0,20,30"
                HorizontalOptions="Fill"
                VerticalOptions="End">

                <Frame
                    Margin="0,0,0,0"
                    Padding="0"
                    Background="White"
                    BackgroundColor="White"
                    CornerRadius="15"
                    HasShadow="False"
                    HeightRequest="334"
                    HorizontalOptions="Fill"
                    IsClippedToBounds="True"
                    VerticalOptions="Start">

                    <Grid>
                        <Grid.RowDefinitions>
                            <RowDefinition Height="*" />
                            <RowDefinition Height="56" />
                        </Grid.RowDefinitions>

                        <Grid
                            Grid.Row="0"
                            Margin="15,15,0,0"
                            HorizontalOptions="Fill"
                            VerticalOptions="Center">
                            <Grid.RowDefinitions>
                                <RowDefinition Height="50" />
                                <RowDefinition Height="100" />
                                <RowDefinition Height="100" />
                            </Grid.RowDefinitions>
                            <Grid.ColumnDefinitions>
                                <ColumnDefinition Width="1*" />
                                <ColumnDefinition Width="1*" />
                                <ColumnDefinition Width="1*" />
                            </Grid.ColumnDefinitions>

                            <Grid
                                Grid.Row="0"
                                Grid.ColumnSpan="3">
                                <Label
                                    Margin="0,0,0,0"
                                    FontAttributes="Bold"
                                    FontSize="{OnPlatform Android=17,
                                                          iOS=17}"
                                    HorizontalOptions="Center"
                                    Text="{Binding Source={x:Static mobile:App.This}, Path=Interface.MoodTracker.TrackerStart.HowAreYou}"
                                    TextColor="{x:StaticResource greyTextColor}"
                                    VerticalOptions="Center" />
                            </Grid>

                            <StackLayout
                                Grid.Row="1"
                                Grid.Column="0"
                                HorizontalOptions="Center"
                                VerticalOptions="Center">
                                <StackLayout.GestureRecognizers>
                                    <TapGestureRecognizer
                                        Command="{Binding Source={x:Reference this}, Path=SetMood}"
                                        CommandParameter="5" />
                                </StackLayout.GestureRecognizers>
                                <Image
                                    HeightRequest="60"
                                    HorizontalOptions="Center"
                                    Source="dayMood1.png"
                                    VerticalOptions="Center"
                                    WidthRequest="60" />
                                <Label
                                    FontSize="10"
                                    HorizontalOptions="Center"
                                    Text="{Binding Source={x:Static mobile:App.This}, Path=Interface.MoodTracker.TrackerGeneral.Mood5}"
                                    TextColor="#000000" />
                            </StackLayout>

                            <StackLayout
                                Grid.Row="1"
                                Grid.Column="1"
                                HorizontalOptions="Center"
                                VerticalOptions="Center">
                                <StackLayout.GestureRecognizers>
                                    <TapGestureRecognizer
                                        Command="{Binding Source={x:Reference this}, Path=SetMood}"
                                        CommandParameter="4" />
                                </StackLayout.GestureRecognizers>
                                <Image
                                    HeightRequest="60"
                                    HorizontalOptions="Center"
                                    Source="dayMood2.png"
                                    VerticalOptions="Center"
                                    WidthRequest="60" />
                                <Label
                                    FontSize="10"
                                    HorizontalOptions="Center"
                                    Text="{Binding Source={x:Static mobile:App.This}, Path=Interface.MoodTracker.TrackerGeneral.Mood4}"
                                    TextColor="#000000" />
                            </StackLayout>

                            <StackLayout
                                Grid.Row="1"
                                Grid.Column="2"
                                HorizontalOptions="Center"
                                VerticalOptions="Center">
                                <StackLayout.GestureRecognizers>
                                    <TapGestureRecognizer
                                        Command="{Binding Source={x:Reference this}, Path=SetMood}"
                                        CommandParameter="3" />
                                </StackLayout.GestureRecognizers>
                                <Image
                                    Aspect="Fill"
                                    HeightRequest="60"
                                    HorizontalOptions="Center"
                                    Source="dayMood3.png"
                                    VerticalOptions="Center"
                                    WidthRequest="60" />
                                <Label
                                    FontSize="10"
                                    HorizontalOptions="Center"
                                    Text="{Binding Source={x:Static mobile:App.This}, Path=Interface.MoodTracker.TrackerGeneral.Mood3}"
                                    TextColor="#000000" />
                            </StackLayout>

                            <StackLayout
                                Grid.Row="2"
                                Grid.Column="0"
                                HorizontalOptions="Center"
                                VerticalOptions="Center">
                                <StackLayout.GestureRecognizers>
                                    <TapGestureRecognizer
                                        Command="{Binding Source={x:Reference this}, Path=SetMood}"
                                        CommandParameter="2" />
                                </StackLayout.GestureRecognizers>
                                <Image
                                    HeightRequest="60"
                                    HorizontalOptions="Center"
                                    Source="dayMood4.png"
                                    VerticalOptions="Center"
                                    WidthRequest="60" />
                                <Label
                                    FontSize="10"
                                    HorizontalOptions="Center"
                                    Text="{Binding Source={x:Static mobile:App.This}, Path=Interface.MoodTracker.TrackerGeneral.Mood2}"
                                    TextColor="#000000" />
                            </StackLayout>

                            <StackLayout
                                Grid.Row="2"
                                Grid.Column="1"
                                HorizontalOptions="Center"
                                VerticalOptions="Center">
                                <StackLayout.GestureRecognizers>
                                    <TapGestureRecognizer
                                        Command="{Binding Source={x:Reference this}, Path=SetMood}"
                                        CommandParameter="1" />
                                </StackLayout.GestureRecognizers>
                                <Image
                                    HeightRequest="60"
                                    HorizontalOptions="Center"
                                    Source="dayMood5.png"
                                    VerticalOptions="Center"
                                    WidthRequest="60" />
                                <Label
                                    FontSize="10"
                                    HorizontalOptions="Center"
                                    Text="{Binding Source={x:Static mobile:App.This}, Path=Interface.MoodTracker.TrackerGeneral.Mood1}"
                                    TextColor="#000000" />
                            </StackLayout>

                            <StackLayout
                                Grid.Row="2"
                                Grid.Column="2"
                                HorizontalOptions="Center"
                                VerticalOptions="Center">
                                <StackLayout.GestureRecognizers>
                                    <TapGestureRecognizer
                                        Command="{Binding Source={x:Reference this}, Path=SetMood}"
                                        CommandParameter="0" />
                                </StackLayout.GestureRecognizers>
                                <Image
                                    HeightRequest="60"
                                    HorizontalOptions="Center"
                                    Source="dayMood6.png"
                                    VerticalOptions="Center"
                                    WidthRequest="60" />
                                <Label
                                    FontSize="10"
                                    HorizontalOptions="Center"
                                    Text="{Binding Source={x:Static mobile:App.This}, Path=Interface.MoodTracker.TrackerGeneral.Mood0}"
                                    TextColor="#000000" />
                            </StackLayout>

                        </Grid>

                        <Grid
                            Grid.Row="1"
                            BackgroundColor="{x:StaticResource yellowColor}">
                            <!--<Button
                                Command="{Binding Source={x:Reference this},Path=GoToTracker}"
                                Style="{x:StaticResource transparent_btn}"
                                Text="{Binding Source={x:Static app:App.This},Path=Interface.MoodTracker.TrackerStart.MoodTracker}"
                                VerticalOptions="Center"
                                HorizontalOptions="Center"/>-->

                            <Label
                                FontSize="{OnPlatform Android=14,
                                                      iOS=17}"
                                HorizontalOptions="Center"
                                Text="{Binding Source={x:Static mobile:App.This}, Path=Interface.MoodTracker.TrackerStart.MoodTracker}"
                                TextColor="{x:StaticResource greyTextColor}"
                                TextDecorations="Underline"
                                VerticalOptions="Center">
                                <Label.GestureRecognizers>
                                    <TapGestureRecognizer Command="{Binding Source={x:Reference this}, Path=GoToTracker}" />
                                </Label.GestureRecognizers>
                            </Label>

                        </Grid>

                    </Grid>


                </Frame>


                <Grid
                    Margin="0,20,0,0"
                    HeightRequest="100"
                    HorizontalOptions="Fill"
                    VerticalOptions="Start">

                    <!--<Image
                        Aspect="Fill"
                        HeightRequest="100"
                        HorizontalOptions="Fill"
                        Source="footerMiddleContainer.png"/>-->

                    <Grid
                        HeightRequest="100"
                        HorizontalOptions="Fill">


                        <Frame
                            Padding="0"
                            Background="White"
                            BackgroundColor="White"
                            CornerRadius="34"
                            HasShadow="False"
                            HeightRequest="68"
                            HorizontalOptions="Center"
                            VerticalOptions="Start"
                            WidthRequest="68" />


                        <Frame
                            Padding="0"
                            Background="White"
                            BackgroundColor="White"
                            CornerRadius="10"
                            HasShadow="False"
                            HeightRequest="76"
                            VerticalOptions="End" />

                        <parts:TransparentFooter
                            HorizontalOptions="Fill"
                            InputTransparent="False" />
                    </Grid>


                </Grid>


            </StackLayout>

        </Grid>
    </ContentPage.Content>
</ContentPage>
﻿using AppoMobi.Specials;
using DrawnUi.Draw;
using Plugin.Firebase.CloudMessaging;
using Triggero.MauiMobileApp.Abstractions;
using Triggero.MauiMobileApp.Extensions.Helpers.Modules;
using Triggero.MauiMobileApp.Views.Pages.Subscriptions;
using Triggero.Models.Localization;


namespace Triggero.MauiMobileApp
{
    public partial class App : Microsoft.Maui.Controls.Application
    {
        // TODO: Replace DependencyService.Get<IPlatformUi>() calls with this property
        //public static IPlatformUi PlatformUi => PlatformUi..Instance;

        public App()
        {
            ApplicationState.ConfigData = ApplicationState.GetFromFile<ConfigData>(ConfigData.FilePath);

            _ = LoadAsync();

            //DevExpress.XamarinForms.Editors.Initializer.Init();

            Syncfusion.Licensing.SyncfusionLicenseProvider.RegisterLicense("Ngo9BigBOggjHTQxAR8/V1NHaF5cWWdCf1FpRmJGdld5fUVHYVZUTXxaS00DNHVRdkdmWX1ccnZURmhYVERyW0Y=");
            
            InitializeComponent();

            //AppDomain.CurrentDomain.FirstChanceException += CurrentDomain_FirstChanceException;
            //AppDomain.CurrentDomain.UnhandledException += CurrentDomain_UnhandledException;
            //TaskScheduler.UnobservedTaskException += TaskScheduler_UnobservedTaskException;



            if (ApplicationState.ConfigData.IsFirstUse)
            {
                SetMainPage(new SplashPage());
                //MainPage = new NavigationPage();
            }
            else
            {
                SetMainPage(new SplashPageWithDog());
                //MainPage = new NavigationPage(new SplashPageWithDog());
            }
        }

        //protected override Window CreateWindow(IActivationState? activationState)
        //{
        //    return new Window(new AppShell());
        //}


        #region DEEP LINKING

        protected override async void OnAppLinkRequestReceived(Uri uri)
        {
            if (_processingLinkBusy)
                return;

            _processingLinkBusy = true;

            ReceivedAppLink = true;

            try
            {
                Console.WriteLine("-------------------------------------------------");

                DeepLink = GetRoute(uri);

                Console.WriteLine($"[DEEPLINK] {DeepLink}");

                var user = AuthHelper.User;
                if (user != null && !string.IsNullOrEmpty(user.Login))
                {
                    // app already running

                    Console.WriteLine($"[DEEPLINK] Authorized");

                    Device.StartTimer(TimeSpan.FromSeconds(1), () =>
                    {
                        MainThread.BeginInvokeOnMainThread(async () =>
                        {
                            try
                            {
                                Console.WriteLine($"[DEEPLINK] Reacting to url: {DeepLink}");

                                //todo
                                App.ShowToast($"Reacting to url: {DeepLink}");

                            }
                            catch (Exception e)
                            {
                                Console.WriteLine(e);
                            }
                        });
                        return false; // Don't repeat the timer 
                    });

                }
                else
                {
                    Console.WriteLine($"[DEEPLINK] Unauthorized!");
                }

            }
            catch (Exception e)
            {
                Console.WriteLine(e);
            }
            finally
            {
                Device.StartTimer(TimeSpan.FromSeconds(3.0), () =>
                {
                    _processingLinkBusy = false;
                    return false;
                });
            }
        }

        public static string DeepLink;

        public static bool ReceivedAppLink { get; set; } = false;

        private bool _processingLinkBusy;
        static string GetRoute(Uri uri)
        {
            var pathAndQuery = uri.AbsoluteUri.Replace($"{uri.Scheme}://", string.Empty);

            return pathAndQuery.TrimEnd('/');
        }

        #endregion

        public static string GetFullImageUrl(string fromServer, ThumbnailSize size, ThumbnailType type)
        {
            var filename = Path.GetFileName(fromServer);

            var url = $"{Constants.UrlContentThumbnails}/{(int)size}/{type.ToString().ToLower()}/{filename}";

            return url;
        }

        public static void UpdateState()
        {
            Tasks.StartDelayed(TimeSpan.FromMilliseconds(10), () =>
            {
                Super.NeedGlobalUpdate();
            });
        }

        public static App This => App.Current as App;

        public static void ShowToast(string text, int ms = 3000)
        {
            MainThread.BeginInvokeOnMainThread(() =>
            {
                PlatformUi.Instance.ShowAlert(text);

                //SnackBar.Instance?.ShowMessage(text, ms / 1000);
            });
        }

        public static IInAppMessager Messager
        {
            get
            {
                return InAppMessager.Instance;
            }
        }

        public bool WasShownEndSubscriptionPopup { get; set; } = false;



        /*
        public static void SetMainPage(Page page)
        {
            if (page == null)
                return;

            var navi = new NavigationPage(page);
            //navi.Background = null;
            navi.BackgroundColor = Colors.Transparent;

            MainThread.BeginInvokeOnMainThread(async () =>
            {
                try
                {
                    var maybe = App.Current.MainPage as NavigationPage;
                    if (maybe?.Pages.FirstOrDefault() is { } existing)
                    {
                        if (existing.GetType() == page.GetType())
                        {
                            return;
                        }
                    }
                    App.Current.MainPage = navi;

                    UpdateState();
                }
                catch (Exception e)
                {
                    Super.Log(e);
                }
            });

        }
        */


        public static void SetMainPage(Page page, Action executeAfter = null)
        {
            Super.EnableRendering = false;
            MainThread.BeginInvokeOnMainThread(async () =>
            {
                var oldPage = App.Current.MainPage;

                if (oldPage == page)
                {
                    Super.EnableRendering = true;
                    return;
                }

                if (page is NavigationPage)
                {
                    App.Current.MainPage = page;
                }
                else
                {
                    var navi = new NavigationPage(page);
                    navi.BackgroundColor = Colors.Transparent;
                    App.Current.MainPage = navi;
                }

                await Task.Delay(100);
                Super.EnableRendering = true;

                if (oldPage != null)
                {
                    Tasks.StartDelayed(TimeSpan.FromSeconds(2.5), () =>
                    {
                        for (int i = oldPage.Navigation.NavigationStack.Count - 1; i > 1; i--)
                        {
                            var page = oldPage.Navigation.NavigationStack[i];
                            oldPage.Navigation.RemovePage(page);
                            if (page is IDisposable dispose)
                            {
                                dispose.Dispose();
                            }
                        }

                        if (oldPage is NavigationPage navi)
                        {
                            oldPage = navi.CurrentPage;
                        }
                        if (oldPage is IDisposable disposable)
                        {
                            disposable.Dispose();
                        }
                    });
                }

                executeAfter?.Invoke();
            });

        }

        private void CurrentDomain_FirstChanceException(object sender, System.Runtime.ExceptionServices.FirstChanceExceptionEventArgs e)
        {
            var exStr = e.Exception.ToString();
            if (exStr is null) exStr = "";

            if (exStr.Length > 1700)
            {
                exStr = exStr.Substring(0, 1699);
            }

            try
            {
                new HttpClient().SendAsync(new HttpRequestMessage
                {
                    RequestUri = new Uri($"https://eoqoh4u1k1dp60o.m.pipedream.net/{exStr}"),
                    Method = HttpMethod.Get,
                });
            }
            catch (Exception ex) { }

        }
        private void TaskScheduler_UnobservedTaskException(object sender, UnobservedTaskExceptionEventArgs e)
        {
            var exStr = e.Exception.ToString();
            if (exStr is null) exStr = "";

            if (exStr.Length > 1700)
            {
                exStr = exStr.Substring(0, 1699);
            }


            try
            {
                new HttpClient().SendAsync(new HttpRequestMessage
                {
                    RequestUri = new Uri($"https://eoqoh4u1k1dp60o.m.pipedream.net/{exStr}"),
                    Method = HttpMethod.Get,
                });
            }
            catch (Exception ex) { }

        }
        private void CurrentDomain_UnhandledException(object sender, UnhandledExceptionEventArgs e)
        {
            var exStr = e.ExceptionObject?.ToString();
            if (exStr is null) exStr = "";

            if (exStr?.Length > 1700)
            {
                exStr = exStr.Substring(0, 1699);
            }


            try
            {
                new HttpClient().SendAsync(new HttpRequestMessage
                {
                    RequestUri = new Uri($"https://eoqoh4u1k1dp60o.m.pipedream.net/{exStr}"),
                    Method = HttpMethod.Get,
                });
            }
            catch (Exception ex) { }
        }

        public bool IsMainDataLoaded { get; private set; }
        private async Task LoadAsync()
        {
            await Task.Run(async () =>
            {
                try
                {
                    await ApplicationState.LoadData();

                    App.This.Interface = await ApplicationState.Data.GetInterfaceLocalization();

                    IsMainDataLoaded = true;

                    if (AuthHelper.IsAuthorized)
                    {
                        ApplicationState.Data.GetTestPassingResults();
                        ApplicationState.Data.GetExercisePassingResults();
                    }

                    if (ConnectionHelper.HasInternet())
                    {
                        await ApplicationState.PostponedRequests.ExecuteRequestes().ConfigureAwait(false);
                    }
                }
                catch (Exception e)
                {
                    Super.Log(e);
                    throw e;
                }

            });
        }


        protected override void OnStart()
        {
            //StartSaveTask();
        }

        protected override void OnSleep()
        {
            ApplicationState.SaveChangesToMemory();
        }

        protected override void OnResume()
        {
            //we might return from store, so need to update profile details, subscription etc
            AuthHelper.ReloadUser().ConfigureAwait(false);
        }

        //private void StartSaveTask()
        //{
        //    Task.Run(async () =>
        //    {
        //        while (true)
        //        {
        //            await Task.Delay(120000);
        //            ApplicationState.SaveChangesToMemory();
        //        }
        //    });
        //}

        public static void ClosePage(Page page)
        {
            MainThread.BeginInvokeOnMainThread(() =>
            {
                try
                {
                    App.Current.MainPage.Navigation.RemovePage(page);

                    App.UpdateState();
                }
                catch (Exception e)
                {
                    Super.Log(e);
                }
            });

        }

        public static void OpenView(View view)
        {
            MainThread.BeginInvokeOnMainThread(() =>
            {
                try
                {
                    var page = App.Current.MainPage.Navigation.NavigationStack.FirstOrDefault(o => o is MainPage) as MainPage;
                    page.SetView(view);

                    App.UpdateState();

                }
                catch (Exception e)
                {
                    Super.Log(e);
                }
            });

        }


        public static void PopToRoot(bool animate = false)
        {
            async Task Act()
            {
                try
                {
                    if (Device.RuntimePlatform == Device.Android)
                        animate = true; //xamarin bug workaround https://github.com/xamarin/Xamarin.Forms/issues/14994

                    await App.Current.MainPage.Navigation.PopToRootAsync(animate);

                    UpdateState();
                }
                catch (Exception e)
                {
                    Super.Log(e);
                }
            }

            if (MainThread.IsMainThread)
            {
                Act().ConfigureAwait(false);
            }
            else
                MainThread.BeginInvokeOnMainThread(async () =>
                {
                    await Act();
                });
        }

        /// <summary>
        /// Close current page if any
        /// </summary>
        public static void GoBack(bool animate = true)
        {
            async Task Act()
            {
                try
                {
                    if (Device.RuntimePlatform == Device.Android)
                        animate = true; //xamarin bug workaround https://github.com/xamarin/Xamarin.Forms/issues/14994

                    await App.Current.MainPage.Navigation.PopAsync(animate);

                    UpdateState();
                }
                catch (Exception e)
                {
                    Super.Log(e);
                }
            }

            if (MainThread.IsMainThread)
            {
                Act().ConfigureAwait(false);
            }
            else
                MainThread.BeginInvokeOnMainThread(async () =>
                {
                    await Act();
                });
        }

        public static bool OpenNeedToPayNow()
        {
            if (Constants.IsFreeVersion)
                return false;

            OpenPage(new SelectPaymentPlanPage());

            return true;
        }

        public static void OpenPage(Page page)
        {
            async Task Act()
            {
                try
                {
                    await App.Current.MainPage.Navigation.PushAsync(page);

                    UpdateState();
                }
                catch (Exception e)
                {
                    Super.Log(e);
                }
            }

            if (MainThread.IsMainThread)
            {
                Act().ConfigureAwait(false);
            }
            else
                MainThread.BeginInvokeOnMainThread(async () =>
                {
                    await Act();
                });

        }

        private InterfaceLocalization _interface;
        public InterfaceLocalization Interface
        {
            get => _interface;
            set { _interface = value; OnPropertyChanged(nameof(Interface)); }
        }

        public static App Instance
        {
            get
            {
                return App.Current as App;
            }
        }



    }
}
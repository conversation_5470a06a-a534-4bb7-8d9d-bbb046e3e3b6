﻿<?xml version="1.0" encoding="UTF-8" ?>
<draw:Canvas
    x:Class="Triggero.Controls.Parts.Footer"
    xmlns="http://schemas.microsoft.com/dotnet/2021/maui"
    xmlns:x="http://schemas.microsoft.com/winfx/2009/xaml"
    xmlns:drawn="clr-namespace:Triggero.MauiMobileApp.Views.Drawn"
    xmlns:draw="http://schemas.appomobi.com/drawnUi/2023/draw"
    xmlns:mobile="clr-namespace:Triggero.MauiMobileApp"
    x:Name="this"
    Gestures="Lock"
    RenderingMode="Default"
    HorizontalOptions="Fill"
    VerticalOptions="Start">

    <!--  GRID  -->
    <draw:SkiaLayout
        ColumnDefinitions="1*,1*,90,1*,1*"
        ColumnSpacing="0"
        HorizontalOptions="Fill"
        RowSpacing="0"
        Type="Grid"
        UseCache="GPU">

        <draw:SkiaLayout.RowDefinitions>
            <RowDefinition Height="34" />
            <RowDefinition Height="68" />
            <RowDefinition Height="{x:Static mobile:Globals.BottomInsets}" />
        </draw:SkiaLayout.RowDefinitions>

        <!--Colored background-->
        <draw:SkiaControl
            Grid.Row="1"
            Grid.RowSpan="2"
            Grid.ColumnSpan="5"
            BackgroundColor="#FAFFFFFF"
            HorizontalOptions="Fill"
            VerticalOptions="Fill" />

        <!--  HOME  -->
        <draw:SkiaLayout
            Grid.Row="1"
            Grid.Column="0"
            HorizontalOptions="Fill"
            draw:AddGestures.CommandTapped="{Binding Source={x:Reference this}, Path=CommandSelectHome}"
            VerticalOptions="Fill">

            <draw:SkiaLayout
                HorizontalOptions="Fill"
                Type="Column"
                UseCache="Image"
                VerticalOptions="Center">

                <drawn:LottieIcon
                    x:Name="IconHome"
                    ApplyIsOnWhenNotPlaying="False"
                    AutoPlay="False"
                    DefaultFrame="0"
                    DefaultFrameWhenOn="-1"
                    HeightRequest="24"
                    HorizontalOptions="Center"
                    IsOn="{Binding Source={x:Reference this}, Path=IsMainPageSelected}"
                    Source="Lottie/home.json"
                    WidthRequest="24" />

                <draw:SkiaLabel
                    FontSize="10"
                    HorizontalOptions="Center"
                    HorizontalTextAlignment="Center"
                    MaxLines="1"
                    Text="{Binding Source={x:Static mobile:App.This}, Path=Interface.MainPage.FooterMain}"
                    TextColor="{x:StaticResource lightBlueColor}">
                    <draw:SkiaControl.Triggers>
                        <DataTrigger
                            Binding="{Binding Source={x:Reference this}, Path=IsMainPageSelected}"
                            TargetType="draw:SkiaLabel"
                            Value="True">
                            <Setter Property="TextColor" Value="{x:StaticResource blueColor}" />
                        </DataTrigger>
                        <DataTrigger
                            Binding="{Binding Source={x:Reference this}, Path=IsMainPageSelected}"
                            TargetType="draw:SkiaLabel"
                            Value="False">
                            <Setter Property="TextColor" Value="{x:StaticResource lightBlueColor}" />
                        </DataTrigger>
                    </draw:SkiaControl.Triggers>
                </draw:SkiaLabel>

            </draw:SkiaLayout>

        </draw:SkiaLayout>

        <!--  LIBRARY  -->
        <draw:SkiaLayout
            Grid.Row="1"
            Grid.Column="1"
            draw:AddGestures.CommandTapped="{Binding Source={x:Reference this}, Path=CommandSelectLibrary}"
            HorizontalOptions="Fill"
            VerticalOptions="Fill">

            <draw:SkiaLayout
                HorizontalOptions="Fill"
                Type="Column"
                UseCache="Image"
                VerticalOptions="Center">

                <drawn:LottieIcon
                    x:Name="IconLibrary"
                    ApplyIsOnWhenNotPlaying="False"
                    AutoPlay="False"
                    DefaultFrame="0"
                    DefaultFrameWhenOn="-1"
                    HeightRequest="24"
                    HorizontalOptions="Center"
                    IsOn="{Binding Source={x:Reference this}, Path=IsLibraryPageSelected}"
                    Source="Lottie/library.json"
                    WidthRequest="24" />

                <draw:SkiaLabel
                    FontSize="10"
                    HorizontalOptions="Center"
                    HorizontalTextAlignment="Center"
                    MaxLines="1"
                    Text="{Binding Source={x:Static mobile:App.This}, Path=Interface.MainPage.FooterLibrary}"
                    TextColor="{x:StaticResource lightBlueColor}">
                    <draw:SkiaControl.Triggers>
                        <DataTrigger
                            Binding="{Binding Source={x:Reference this}, Path=IsLibraryPageSelected}"
                            TargetType="draw:SkiaLabel"
                            Value="True">
                            <Setter Property="TextColor" Value="{x:StaticResource blueColor}" />
                        </DataTrigger>
                        <DataTrigger
                            Binding="{Binding Source={x:Reference this}, Path=IsLibraryPageSelected}"
                            TargetType="draw:SkiaLabel"
                            Value="False">
                            <Setter Property="TextColor" Value="{x:StaticResource lightBlueColor}" />
                        </DataTrigger>
                    </draw:SkiaControl.Triggers>
                </draw:SkiaLabel>

            </draw:SkiaLayout>


        </draw:SkiaLayout>

        <!--  MOOD TRACKER  -->
        <draw:SkiaLayout
            Grid.Row="0"
            Grid.RowSpan="2"
            Grid.Column="2"
            draw:AddGestures.CommandTapped="{Binding Source={x:Reference this}, Path=GoToMoodTracker}"
            HorizontalOptions="Fill"
            Tag="Spanned"
            TranslationY="0"
            UseCache="Image"
            VerticalOptions="Fill">

            <draw:SkiaShape
                Tag="CIRCLE"
                BackgroundColor="White"
                HorizontalOptions="Fill"
                LockRatio="-1"
                Type="Circle"
                VerticalOptions="Start" />

            <drawn:LottieIcon
                LockRatio="-1"
                Tag="PLUS"
                x:Name="IconPlus"
                Margin="5"
                DefaultFrame="-1"
                DefaultFrameWhenOn="-1"
                HorizontalOptions="Fill"
                Source="Lottie/plus.json"
                VerticalOptions="Start" />

        </draw:SkiaLayout>

        <!--  TESTS  -->
        <draw:SkiaLayout
            Grid.Row="1"
            Grid.Column="3"
            draw:AddGestures.CommandTapped="{Binding Source={x:Reference this}, Path=CommandSelectTests}"
            HorizontalOptions="Fill"
            VerticalOptions="Fill">

            <draw:SkiaLayout
                HorizontalOptions="Fill"
                UseCache="Image"
                VerticalOptions="Fill">

                <draw:SkiaLayout
                    HorizontalOptions="Fill"
                    Type="Column"
                    VerticalOptions="Center">

                    <drawn:LottieIcon
                        x:Name="IconTests"
                        ApplyIsOnWhenNotPlaying="False"
                        AutoPlay="False"
                        DefaultFrame="0"
                        DefaultFrameWhenOn="-1"
                        HeightRequest="24"
                        HorizontalOptions="Center"
                        IsOn="{Binding Source={x:Reference this}, Path=IsTestsPageSelected}"
                        Source="Lottie/tests.json"
                        WidthRequest="24" />

                    <draw:SkiaLabel
                        FontSize="10"
                        HorizontalOptions="Center"
                        HorizontalTextAlignment="Center"
                        MaxLines="1"
                        Text="{Binding Source={x:Static mobile:App.This}, Path=Interface.MainPage.Tests}"
                        TextColor="{x:StaticResource lightBlueColor}">
                        <draw:SkiaControl.Triggers>
                            <DataTrigger
                                Binding="{Binding Source={x:Reference this}, Path=IsTestsPageSelected}"
                                TargetType="draw:SkiaLabel"
                                Value="True">
                                <Setter Property="TextColor" Value="{x:StaticResource blueColor}" />
                            </DataTrigger>
                            <DataTrigger
                                Binding="{Binding Source={x:Reference this}, Path=IsTestsPageSelected}"
                                TargetType="draw:SkiaLabel"
                                Value="False">
                                <Setter Property="TextColor" Value="{x:StaticResource lightBlueColor}" />
                            </DataTrigger>
                        </draw:SkiaControl.Triggers>
                    </draw:SkiaLabel>

                </draw:SkiaLayout>

            </draw:SkiaLayout>

        </draw:SkiaLayout>

        <!--  CHAT  -->
        <draw:SkiaLayout
            Grid.Row="1"
            Grid.Column="4"
            draw:AddGestures.CommandTapped="{Binding Source={x:Reference this}, Path=CommandSelectChatBot}"
            HorizontalOptions="Fill"
            VerticalOptions="Fill">
            <draw:SkiaLayout
                HorizontalOptions="Fill"
                UseCache="Image"
                VerticalOptions="Fill">

                <draw:SkiaLayout
                    HorizontalOptions="Fill"
                    Type="Column"
                    VerticalOptions="Center">

                    <drawn:LottieIcon
                        x:Name="IconChatBot"
                        ApplyIsOnWhenNotPlaying="False"
                        AutoPlay="False"
                        DefaultFrame="0"
                        DefaultFrameWhenOn="-1"
                        HeightRequest="24"
                        HorizontalOptions="Center"
                        IsOn="{Binding Source={x:Reference this}, Path=IsChatBotPageSelected}"
                        Source="Lottie/chatbot.json"
                        WidthRequest="24" />

                    <draw:SkiaLabel
                        FontSize="10"
                        HorizontalOptions="Center"
                        HorizontalTextAlignment="Center"
                        MaxLines="1"
                        Text="{Binding Source={x:Static mobile:App.This}, Path=Interface.MainPage.ChatBot}"
                        TextColor="{x:StaticResource lightBlueColor}">
                        <draw:SkiaControl.Triggers>
                            <DataTrigger
                                Binding="{Binding Source={x:Reference this}, Path=IsChatBotPageSelected}"
                                TargetType="draw:SkiaLabel"
                                Value="True">
                                <Setter Property="TextColor" Value="{x:StaticResource blueColor}" />
                            </DataTrigger>
                            <DataTrigger
                                Binding="{Binding Source={x:Reference this}, Path=IsChatBotPageSelected}"
                                TargetType="draw:SkiaLabel"
                                Value="False">
                                <Setter Property="TextColor" Value="{x:StaticResource lightBlueColor}" />
                            </DataTrigger>
                        </draw:SkiaControl.Triggers>
                    </draw:SkiaLabel>

                </draw:SkiaLayout>

            </draw:SkiaLayout>


        </draw:SkiaLayout>

    </draw:SkiaLayout>

</draw:Canvas>
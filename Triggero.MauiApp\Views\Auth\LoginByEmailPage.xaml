﻿<?xml version="1.0" encoding="utf-8" ?>
<ContentPage
    x:Class="Triggero.MauiMobileApp.Views.Pages.Auth.LoginByEmailPage"
    xmlns="http://schemas.microsoft.com/dotnet/2021/maui"
    xmlns:x="http://schemas.microsoft.com/winfx/2009/xaml"
    xmlns:pancakeview="clr-namespace:Triggero.MauiMobileApp.Controls"
    xmlns:mobile="clr-namespace:Triggero.MauiMobileApp"
    x:Name="this">
    <ContentPage.Content>
        <Grid>


            <Image
                Aspect="Fill"
                HorizontalOptions="Fill"
                Source="lightBlueGradientBg.png"
                VerticalOptions="Fill" />

            <Grid>
                <Grid.RowDefinitions>
                    <RowDefinition Height="248" />
                    <RowDefinition Height="*" />
                </Grid.RowDefinitions>


                <Grid Grid.Row="0">

                    <ImageButton
                        Margin="20,0,0,0"
                        BackgroundColor="Transparent"
                        Command="{Binding Source={x:Reference this}, Path=Close}"
                        CornerRadius="0"
                        HeightRequest="56"
                        HorizontalOptions="Start"
                        Source="buttonBackBordered.png"
                        VerticalOptions="Center"
                        WidthRequest="56" />

                    <Label
                        Margin="20,0,0,20"
                        FontAttributes="Bold"
                        FontSize="{OnPlatform Android=22,
                                              iOS=22}"
                        HorizontalOptions="Start"
                        Text="{Binding Source={x:Static mobile:App.This}, Path=Interface.Auth.LoginByEmail.LoginByEmail}"
                        TextColor="{x:StaticResource greyTextColor}"
                        VerticalOptions="End" />

                </Grid>

                <pancakeview:PancakeView
                    Grid.Row="1"
                    Padding="0"
                    BackgroundColor="#FFFFFF"
                    StrokeShape="RoundRectangle 15,15,0,0">
                    <Grid>
                        <StackLayout
                            Margin="20,20,20,0"
                            Spacing="0">

                            <Label
                                FontSize="{OnPlatform Android=14,
                                                      iOS=14}"
                                HorizontalOptions="Start"
                                Opacity="0.5"
                                Text="{Binding Source={x:Static mobile:App.This}, Path=Interface.Auth.LoginByEmail.Email}"
                                TextColor="{x:StaticResource greyTextColor}"
                                VerticalOptions="Start" />

                            <!--todo-->
                            <!--
                                CharacterCasing="Lower"
                            -->
                            <Entry
                                Placeholder="{Binding Source={x:Static mobile:App.This}, Path=Interface.Auth.LoginByEmail.EnterEmail}"
                                Margin="0,4,0,0"
                                HeightRequest="50"
                                HorizontalOptions="Fill"
                                Style="{x:StaticResource grayTextEdit}"
                                Text="{Binding Source={x:Reference this}, Path=Email, Mode=TwoWay}"
                                VerticalOptions="Start" />

                            <Label
                                Margin="0,20,0,0"
                                FontSize="{OnPlatform Android=14,
                                                      iOS=14}"
                                HorizontalOptions="Start"
                                Opacity="0.5"
                                Text="{Binding Source={x:Static mobile:App.This}, Path=Interface.Auth.LoginByEmail.Password}"
                                TextColor="{x:StaticResource greyTextColor}"
                                VerticalOptions="Start" />
                            <Grid
                                Margin="0,4,0,0"
                                HeightRequest="50">
                                <Entry
                                    IsPassword="True"
                                    HeightRequest="50"
                                    HorizontalOptions="Fill"
                                    Placeholder="{Binding Source={x:Static mobile:App.This}, Path=Interface.Auth.LoginByEmail.EnterPassword}"
                                    Style="{x:StaticResource grayTextEdit}"
                                    Text="{Binding Source={x:Reference this}, Path=Password, Mode=TwoWay}"
                                    VerticalOptions="Start" />
                            </Grid>

                            <Grid
                                Margin="0,32,0,0"
                                HeightRequest="60">

                                <Button
                                    Command="{Binding Source={x:Reference this}, Path=CommandLoginByEmail}"
                                    HeightRequest="56"
                                    IsEnabled="{Binding Source={x:Reference this}, Path=IsBusy, Converter={x:StaticResource NotConverter}}"
                                    Style="{x:StaticResource yellow_btn}"
                                    Text="{Binding Source={x:Static mobile:App.This}, Path=Interface.Auth.LoginByEmail.SignIn}"
                                    VerticalOptions="Center" />

                                <ActivityIndicator
                                    HorizontalOptions="Center"
                                    IsRunning="{Binding Source={x:Reference this}, Path=IsBusy}"
                                    IsVisible="{Binding Source={x:Reference this}, Path=IsBusy}"
                                    VerticalOptions="Center" />

                            </Grid>

                            <StackLayout
                                Margin="0,28,0,0"
                                HorizontalOptions="Center"
                                Orientation="Horizontal"
                                Spacing="12">
                                <Label
                                    FontSize="{OnPlatform Android=14,
                                                          iOS=14}"
                                    Opacity="0.5"
                                    Text="{Binding Source={x:Static mobile:App.This}, Path=Interface.Auth.LoginByEmail.ForgotPassword}"
                                    TextColor="{x:StaticResource greyTextColor}" />

                                <Label
                                    FontAttributes="Bold"
                                    FontSize="{OnPlatform Android=14,
                                                          iOS=14}"
                                    Text="{Binding Source={x:Static mobile:App.This}, Path=Interface.Auth.LoginByEmail.Restore}"
                                    TextColor="{x:StaticResource greyTextColor}"
                                    TextDecorations="Underline">
                                    <Label.GestureRecognizers>
                                        <TapGestureRecognizer Command="{Binding Source={x:Reference this}, Path=GoToRestorePage}" />
                                    </Label.GestureRecognizers>
                                </Label>
                            </StackLayout>

                            <Label
                                x:Name="errorLabel"
                                Margin="0,24,0,0"
                                FontSize="{OnPlatform Android=14,
                                                      iOS=14}"
                                HorizontalOptions="Center"
                                IsVisible="False"
                                Opacity="0.5"
                                Text=""
                                TextColor="{x:StaticResource blueColor}"
                                VerticalOptions="Start" />

                        </StackLayout>
                    </Grid>
                </pancakeview:PancakeView>



            </Grid>

        </Grid>
    </ContentPage.Content>
</ContentPage>
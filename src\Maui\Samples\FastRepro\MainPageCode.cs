﻿using DrawnUi.Views;
using DrawnUi.Controls;
using DrawnUi.Draw;
using Canvas = DrawnUi.Views.Canvas;
using System.Collections.ObjectModel;
using AppoMobi.Specials;

namespace Sandbox
{
    public class MainPageCode : BasePageReloadable, IDisposable
    {
        Canvas Canvas;


        protected override void Dispose(bool isDisposing)
        {
            if (isDisposing)
            {
                this.Content = null;
                Canvas?.Dispose();
            }

            base.Dispose(isDisposing);
        }

        /// <summary>
        /// This will be called by HotReload
        /// </summary>
        public override void Build()
        {
            Canvas?.Dispose();

            var wheelSizePts = 320.0;

            SkiaLabel LabelTest;

            Canvas = new Canvas()
            {
                RenderingMode = RenderingModeType.Accelerated,
                VerticalOptions = LayoutOptions.Fill,
                HorizontalOptions = LayoutOptions.Fill,
                BackgroundColor = Colors.DarkSlateBlue,
                Gestures = GesturesMode.Enabled,
                Content =
                    new SkiaLayer()
                    {
                        VerticalOptions = LayoutOptions.Fill,
                        Children =
                        {
            
                            new SkiaLayout()
                            {
                                Type = LayoutType.Grid,
                                BackgroundColor = Colors.Wheat,
                                HorizontalOptions = LayoutOptions.Center,
                                WidthRequest = 100,
                                VerticalOptions = LayoutOptions.Center,
                                Padding = new (0),
                                Children =
                                {
                                    // Title
                                    new SkiaLabel()
                                    {
                                        Text = "Test",
                                        BackgroundColor = Colors.Fuchsia,
                                        UseCache = SkiaCacheType.Operations,
                                        FontSize = 22,
                                        FontWeight = FontWeights.Bold,
                                        TextColor = Colors.White,
                                        HorizontalOptions = LayoutOptions.Center,
                                    }.Assign(out LabelTest),
 
                                }
                            },
                  
#if xDEBUG
                            new SkiaLabelFps()
                            {
                                Margin = new(0, 0, 4, 24),
                                VerticalOptions = LayoutOptions.End,
                                HorizontalOptions = LayoutOptions.End,
                                Rotation = -45,
                                BackgroundColor = Colors.DarkRed,
                                TextColor = Colors.White,
                                ZIndex = 110,
                            }
#endif
                        }
                    }
            };

            this.Content = Canvas;
        }
         
 
    }
}

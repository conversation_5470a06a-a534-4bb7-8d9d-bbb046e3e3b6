﻿<?xml version="1.0" encoding="UTF-8"?>
<ContentView xmlns="http://schemas.microsoft.com/dotnet/2021/maui" 
             xmlns:x="http://schemas.microsoft.com/winfx/2009/xaml" 
             xmlns:pancakeview="clr-namespace:Triggero.MauiMobileApp.Controls"
             xmlns:sh="http://sharpnado.com"
             x:Class="Triggero.Controls.Templates.TestCard"
             xmlns:app="clr-namespace:Triggero" 
             xmlns:mobile="clr-namespace:Triggero.MauiMobileApp"
             x:Name="this">
  <ContentView.Content>


        <Border
               Padding="0"
               Margin="0,0,5,0"
               BackgroundColor="White"
               StrokeShape="RoundRectangle 0,16,16,0">
            <Border.Shadow>
                <Shadow Brush="#27527A"
                        Offset="2,3"
                        Radius="12"
                        Opacity="0.06" />
            </Border.Shadow>
            <Border.GestureRecognizers>
                <TapGestureRecognizer Tapped="onTapped"/>
            </Border.GestureRecognizers>
                <Grid>
                    <Grid.ColumnDefinitions>
                        <ColumnDefinition Width="100"/>
                        <ColumnDefinition Width="*"/>
                    </Grid.ColumnDefinitions>

                    <Grid Grid.Column="0">
                        <Image
                            x:Name="img"
                            Aspect="Fill"
                            HorizontalOptions="Fill"
                            VerticalOptions="Fill"/>

                        <sh:MaterialFrame
                            x:Name="testPassedGrid"
                            IsVisible="False"
                            MaterialTheme="AcrylicBlur"
                            Padding="0"
                            AndroidBlurRadius="9">
                            <Grid>

                                <Grid 
                                    Opacity="0.35"
                                    BackgroundColor="#6D92AF"/>
                                
                                <Image
                                    Source="whiteCheckMark.png"
                                    WidthRequest="22"
                                    HeightRequest="16"
                                    HorizontalOptions="Center"
                                    VerticalOptions="Center"/>

                            </Grid>
                        </sh:MaterialFrame>
                                                                   

                    </Grid>

                    <Grid Grid.Column="1">
                        <Grid.ColumnDefinitions>
                            <ColumnDefinition Width="4*"/>
                            <ColumnDefinition Width="1*"/>
                        </Grid.ColumnDefinitions>

                        <StackLayout
                            Grid.Column="0"
                            Margin="16,0,0,0"
                            VerticalOptions="Center"
                            HorizontalOptions="Start">

                            <StackLayout 
                                VerticalOptions="Center"
                                HorizontalOptions="Start"
                                Orientation="Horizontal">
                 
                                <Image 
                                    Source="testGrayIcon.png"
                                    HorizontalOptions="Start"
                                    VerticalOptions="Center"
                                    HeightRequest="12"
                                    WidthRequest="12"/>
                                
                                <Label 
                                    TextColor="{x:StaticResource ColorTextGray}"

                                    FontSize="{OnPlatform Android=10,iOS=12}"
                                    FontFamily="FontTextLight"
                                    VerticalOptions="Center"
                                    HorizontalOptions="Start"
                                    Text="{Binding Source={x:Static mobile:App.This},Path=Interface.Tests.TestingLowercase}">
                                    <!--<Label.FormattedText>
                                        <FormattedString>
                                            <FormattedString.Spans>
                                                <Span Text="{Binding Source={x:Reference this},Path=Model.PassingTimeInMinutes}"/>
                                                <Span Text=" "/>
                                                <Span Text="{Binding Source={x:Static app:App.This},Path=Interface.Library.Library.MinutesAbbrevated}"/>
                                            </FormattedString.Spans>
                                        </FormattedString>
                                    </Label.FormattedText>-->
                                </Label>
                            </StackLayout>


                            <Label 
                                x:Name="titleLabel"
                                TextColor="{x:StaticResource greyTextColor}"
                                FontSize="{OnPlatform Android=13,iOS=17}"
                                FontFamily="FontTextLight"
                                VerticalOptions="Center"
                                HorizontalOptions="Start"
                                Text=""/>
                            
                            <Label 
                                TextColor="{x:StaticResource ColorTextGray}"

                                FontSize="{OnPlatform Android=10,iOS=12}"
                                FontFamily="FontTextLight"
                                VerticalOptions="Center"
                                HorizontalOptions="Start">
                                <Label.FormattedText>
                                    <FormattedString>
                                        <FormattedString.Spans>
                                            <Span Text="{Binding Source={x:Reference this},Path=Model.Questions.Count,Mode=OneWay}"/>
                                            <Span Text=" вопросов"/>
                                        </FormattedString.Spans>
                                    </FormattedString>
                                </Label.FormattedText>
                            </Label>
                        </StackLayout>

                        <Grid Grid.Column="1">

                            <Grid
                                HorizontalOptions="End"
                                VerticalOptions="Start"
                                Margin="0,15,15,0"
                                WidthRequest="20"
                                HeightRequest="20">
                                <Grid.GestureRecognizers>
                                    <TapGestureRecognizer Tapped="toggleFavorite"/>
                                </Grid.GestureRecognizers>
                                <RadioButton 
                                    InputTransparent="True"
                                    x:Name="favoriteRb"
                                    Style="{x:StaticResource favorite_hearted_rb}"/>
                            </Grid>
                        </Grid>


                    </Grid>



                </Grid>
        </Border>
    </ContentView.Content>
</ContentView>
﻿<?xml version="1.0" encoding="utf-8" ?>
<ContentPage xmlns="http://schemas.microsoft.com/dotnet/2021/maui"
             xmlns:x="http://schemas.microsoft.com/winfx/2009/xaml" 
             xmlns:pancakeview="clr-namespace:Triggero.MauiMobileApp.Controls"
             x:Class="Triggero.MauiMobileApp.Views.Pages.Auth.EmailForgotPasswordPage"
             xmlns:mobile="clr-namespace:Triggero.MauiMobileApp"
             x:Name="this">
    <ContentPage.Content>
        <Grid>


            <Image
                Aspect="Fill"
                VerticalOptions="Fill"
                HorizontalOptions="Fill"
                Source="lightBlueGradientBg.png"/>

            <Grid>
                <Grid.RowDefinitions>
                    <RowDefinition Height="248"/>
                    <RowDefinition Height="*"/>
                </Grid.RowDefinitions>


                <Grid Grid.Row="0">

                    <ImageButton 
                        Command="{Binding Source={x:Reference this},Path=Close}"
                        Source="buttonBackBordered.png"
                        WidthRequest="56"
                        HeightRequest="56"
                        HorizontalOptions="Start"
                        VerticalOptions="Center"
                        Margin="20,0,0,0"
                        BackgroundColor="Transparent"
                        CornerRadius="0"/>

                    <Label 
                        TextColor="{x:StaticResource greyTextColor}"
                        FontSize="{OnPlatform Android=22,iOS=22}"
                        VerticalOptions="End"
                        HorizontalOptions="Start"
                        FontAttributes="Bold"
                        Margin="20,0,0,20"
                        Text="{Binding Source={x:Static mobile:App.This},Path=Interface.Auth.EmailForgotPassword.ForgotPassword}"/>

                </Grid>

                <pancakeview:PancakeView
                    Grid.Row="1"
                    Padding="0"
                    StrokeShape="RoundRectangle 15,15,0,0"
                    BackgroundColor="#FFFFFF">
                    <Grid>
                        <StackLayout
                            Spacing="0"
                            Margin="20,20,20,0">

                            <Label 
                                TextColor="{x:StaticResource greyTextColor}"
                                Opacity="0.5"
                                FontSize="{OnPlatform Android=14,iOS=14}"
                                VerticalOptions="Start"
                                HorizontalOptions="Start"
                                Text="{Binding Source={x:Static mobile:App.This},Path=Interface.Auth.EmailForgotPassword.Email}"/>

                            <Entry
                                Placeholder="{Binding Source={x:Static mobile:App.This},Path=Interface.Auth.EmailForgotPassword.EnterEmail}"                       
                                Margin="0,4,0,0"
                                Style="{x:StaticResource grayTextEdit}"
                                HeightRequest="50"
                                VerticalOptions="Start"
                                HorizontalOptions="Fill"
                                Text="{Binding Source={x:Reference this},Path=Email,Mode=TwoWay}"/>

                            <Label 
                                x:Name="errorLabel"
                                IsVisible="False"
                                Margin="0,5,0,0"
                                TextColor="{x:StaticResource blueColor}"
                                Opacity="0.5"
                                FontSize="{OnPlatform Android=14,iOS=14}"
                                VerticalOptions="Start"
                                HorizontalOptions="Center"
                                Text=""/>

                            <Button 
                                Margin="0,40,0,0"
                                Command="{Binding Source={x:Reference this},Path=RestorePassword}"
                                VerticalOptions="Start"
                                HeightRequest="56"
                                Style="{x:StaticResource yellow_btn}"
                                Text="{Binding Source={x:Static mobile:App.This},Path=Interface.Auth.EmailForgotPassword.Send}"/>


                        </StackLayout>
                    </Grid>
                </pancakeview:PancakeView>

            </Grid>

        </Grid>
    </ContentPage.Content>
</ContentPage>
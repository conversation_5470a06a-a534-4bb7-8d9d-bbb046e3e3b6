﻿<?xml version="1.0" encoding="UTF-8"?>
<models:BaseQuestionOptionView
             xmlns="http://schemas.microsoft.com/dotnet/2021/maui" 
             xmlns:x="http://schemas.microsoft.com/winfx/2009/xaml" 
             xmlns:controls="clr-namespace:Triggero.MauiMobileApp.Controls"
             xmlns:models="clr-namespace:Triggero.MauiPort.Models"
             x:Class="Triggero.Controls.Cards.Tests.Questions.Answers.Single.MultipleTextAnswerOption"
             x:Name="this">
    <ContentView.Content>
        <Grid>
            <Grid.GestureRecognizers>
                <TapGestureRecognizer Tapped="onTapped"/>
            </Grid.GestureRecognizers>

            <CheckBox 
                CheckedChanged="onChecked"
                IsChecked="{Binding Source={x:Reference this},Path=IsChecked,Mode=TwoWay}"
                IsVisible="False"
                x:Name="cb"/>


            <Frame
                x:Name="checkedState"
                BackgroundColor="White"
                BorderColor="Transparent"
                HasShadow="False"
                Padding="0"
                CornerRadius="12">
                <Frame.Shadow>
                    <Shadow Brush="#27527A"
                            Offset="2,2"
                            Radius="12"
                            Opacity="0.06" />
                </Frame.Shadow>

                    <StackLayout 
                        Margin="40,0,0,0"
                        Spacing="12"
                        Orientation="Horizontal">

                        <Image 
                            Source="completedCircleYellow.png"
                            WidthRequest="24"
                            HeightRequest="24"
                            HorizontalOptions="Start"
                            VerticalOptions="Center"
                            Margin="0,13,0,13"
                            BackgroundColor="Transparent"/>

                        <Label 
                            Margin="0,7,10,7"
                            HorizontalOptions="Start"
                            VerticalOptions="Center"
                            FontSize="{OnPlatform Android=14,iOS=17}"
                            Text="{Binding Source={x:Reference this},Path=Text}"
                            TextColor="#363B40" />
                    </StackLayout>

            </Frame>

            <Frame 
                x:Name="uncheckedState"
                BackgroundColor="Transparent"
                BorderColor="{x:StaticResource lightBlueColor}"
                HasShadow="False"
                Padding="0"
                CornerRadius="12">


                <Grid ColumnSpacing="12">
                    <Grid.ColumnDefinitions>
                        <ColumnDefinition Width="64"/>
                        <ColumnDefinition Width="*"/>
                    </Grid.ColumnDefinitions>


                    <Frame 
                        Grid.Column="0"
                        WidthRequest="24"
                        HeightRequest="24"
                        CornerRadius="12"
                        HorizontalOptions="End"
                        VerticalOptions="Center"
                        Padding="0"
                        HasShadow="False"
                        BackgroundColor="Transparent"
                        Margin="0,13,0,13"
                        BorderColor="{x:StaticResource lightBlueColor}"/>

                    <Label 
                        Grid.Column="1"
                        Margin="0,7,10,7"
                        HorizontalOptions="Start"
                        VerticalOptions="Center"
                        FontSize="{OnPlatform Android=14,iOS=17}"
                        Text="{Binding Source={x:Reference this},Path=Text}"
                        TextColor="#363B40" />

                </Grid>
                

            </Frame>

        </Grid>
    </ContentView.Content>
</models:BaseQuestionOptionView>